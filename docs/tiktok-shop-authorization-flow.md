# TikTok Shop Authorization Flow Documentation

## Overview

This document outlines the complete TikTok Shop authorization flow and user connection process for the TikTok Shop management system. The flow enables users to authorize our application to access their TikTok shops and connect those shops to their user accounts.

## Current System Architecture

### Backend Components
- **TikTok Shop Controller**: Handles authorization endpoints (`/tiktok-shop/tiktok-authorize`)
- **TikTok Shop Service**: Manages authorization flow and shop data persistence
- **TikTok Application Entity**: Stores multiple TikTok applications (app_key, app_secret)
- **TikTok Shop Entity**: Stores authorized shops with userId association
- **TikTok Client Factory**: Creates API clients for different TikTok applications

### Frontend Components
- **Store Management Page**: `/client/tiktok/store/page.tsx` - Lists connected shops
- **Authentication System**: NextAuth.js with Google OAuth and magic link
- **State Management**: Zustand for UI state, NextAuth for authentication

## Authorization Flow Requirements

### 1. Authorization Link Generation & User Guidance

**Current Implementation:**
- <PERSON><PERSON> creates TikTok applications in the system with app_key and app_secret
- System automatically selects available TikTok applications when limits are reached

**Required Implementation:**
- Generate authorization links dynamically based on available TikTok applications
- Authorization link format: `https://services.us.tiktokshop.com/open/authorize?service_id={service_id}`
- **Critical User Warning**: Users MUST open this link in the same browser where they are logged into TikTok Shop to avoid account conflicts

### 2. Callback URL Handling

**Current Implementation:**
- Backend endpoint: `GET /tiktok-shop/tiktok-authorize?app_key={app_key}&code={auth_code}`
- Public endpoint (no authentication required)
- Calls `handleAuthorizationFlow` method to process authorization

**Proposed Enhancement:**
- Callback URL format: `http://localhost:3000/tiktok-authorize/{app_key}?code={auth_code}`
- Frontend callback page at `/tiktok-authorize/[app_key]`

### 3. Backend Authorization Process

**Current Flow (Working):**
1. Receives app_key and auth_code from TikTok callback
2. Validates TikTok application exists in database
3. Exchanges auth_code for access_token using TikTok SDK
4. Retrieves authorized shop list from TikTok API
5. Saves/updates shops in database (without userId initially)
6. Returns shop data

**Gap Identified:**
- Shops are created without userId association during authorization
- No mechanism to connect authorized shops to user accounts

### 4. User Connection Options

**Integrated Connection Flow (Recommended)**
- User completes authorization and sees callback page with shop details
- Embedded login functionality directly on callback page prevents user distrust
- After successful login, automatic redirect to TikTok store list page
- Copy functionality for shop codes enables easy manual connection

**Manual Connection Fallback**
- User can copy shop codes from authorization callback page
- Navigate to "Connect TikTok Store" page to paste and connect shops
- One-by-one connection process for simplicity

## Implementation Plan

### Phase 1: Frontend Callback Page
**File**: `tts-fe-nextjs/src/app/tiktok-authorize/[app_key]/page.tsx`

**Features:**
- Public page (no authentication required)
- Loading state with clear instructions
- Call existing backend endpoint
- Display authorization results with copy functionality
- Embedded login functionality directly on page
- Error handling with next steps

**UI/UX Recommendations:**
```
┌─────────────────────────────────────┐
│ TikTok Shop Authorization           │
├─────────────────────────────────────┤
│ ✓ Authorization Successful!         │
│                                     │
│ Authorized Shops:                   │
│ • Shop Name 1 (FS001) [Copy]       │
│ • Shop Name 2 (EH002) [Copy]       │
│                                     │
│ Sign in to connect shops to your    │
│ account or copy shop codes for      │
│ manual connection later.            │
│                                     │
│ [Embedded Login Tabs Component]     │
│                                     │
│ Manual Connection: Copy shop codes  │
│ and visit "Connect TikTok Store"    │
│ page to paste and connect.          │
└─────────────────────────────────────┘
```

### Phase 2: Enhanced Connect Store Page
**File**: `tts-fe-nextjs/src/app/(client)/client/tiktok/store/connect/page.tsx`
**Access**: Via "Connect TikTok Store" button at `tts-fe-nextjs/src/app/(client)/client/tiktok/store/page.tsx`, line 133

**Features:**
- Authorization link generation with user guidance
- Manual shop connection using shop codes (one-by-one)
- Seamless flow without confusing method separation

**UI/UX Recommendations:**
```
┌─────────────────────────────────────┐
│ Connect TikTok Store                │
├─────────────────────────────────────┤
│ Step 1: Get Authorization           │
│ ⚠️  Important: Open in same browser │
│ where you're logged into TikTok     │
│                                     │
│ Authorization Link:                 │
│ https://services.us.tiktokshop...   │
│ [Copy Link]                         │
│                                     │
│ Step 2: Connect Your Shop           │
│ After authorization, paste your     │
│ shop code here:                     │
│ Shop Code: [_______________]        │
│ [Connect Shop]                      │
│                                     │
│ Instructions:                       │
│ 1. Copy and open authorization link │
│ 2. Approve in TikTok Shop          │
│ 3. Copy shop code from callback     │
│ 4. Return here and paste code      │
└─────────────────────────────────────┘
```

### Phase 3: Backend Enhancements

**Required Endpoints:**
1. `GET /tiktok-shop/authorization-link` - Generate authorization link (static data initially)
   - Returns: `https://services.us.tiktokshop.com/open/authorize?service_id=7420413125780145962`
   - Future: Distribute different authorization links to different users
2. `POST /tiktok-shop/connect-shop` - Connect shop by code to user (one-by-one)

**Admin Management:**
- Create new admin TikTok shop management screen at: `tts-fe-nextjs/src/app/(admin)/admin/tiktokshop`
- Include filtering capabilities for unconnected shops
- Remove `GET /tiktok-shop/unconnected` endpoint in favor of admin UI

**Database Considerations:**
- TikTok Shop entity already has userId field (nullable)
- No schema changes required
- Add indexes for performance if needed

## Security Considerations

### Authentication Flow
- Authorization callback page is public (required by TikTok)
- User authentication happens after authorization
- Shop connection requires authenticated user

### Data Protection
- Sensitive shop data (tokens) excluded from frontend responses
- User can only access their own connected shops
- Admin endpoints for system management

### Error Handling
- Invalid app_key or auth_code validation
- TikTok API error responses
- Duplicate shop connection prevention
- User-friendly error messages

## Testing Strategy

### Manual Testing
1. Create TikTok application in admin panel
2. Generate authorization link
3. Complete TikTok authorization flow
4. Test callback page functionality
5. Test automatic and manual connection methods

### Automated Testing
- Unit tests for authorization service methods
- Integration tests for callback endpoints
- E2E tests for complete authorization flow

## Deployment Considerations

### Environment Configuration
- TikTok application credentials management
- Callback URL configuration for different environments
- Rate limiting for authorization endpoints

### Monitoring
- Track authorization success/failure rates
- Monitor shop connection patterns
- Alert on authorization errors

## Future Enhancements

### Multi-Application Support
- Automatic application selection based on limits
- Load balancing across TikTok applications
- Application health monitoring

### User Experience
- Progress indicators for authorization flow
- Email notifications for successful connections
- Bulk shop management features

### Analytics
- Authorization flow analytics
- Shop connection success rates
- User behavior tracking

## Conclusion

This authorization flow provides a secure and user-friendly way to connect TikTok shops to user accounts while maintaining the existing system architecture. The implementation focuses on enhancing the user experience while leveraging the robust backend infrastructure already in place.
