# TikTok Shop Project

This is the main repository for the TikTok Shop project. The project is split into two separate repositories:

1. **Backend (NestJS)**: [https://github.com/vinhWater/tts-be-nestjs](https://github.com/vinhWater/tts-be-nestjs)
2. **Frontend (NextJS)**: [https://github.com/vinhWater/tts-fe-nextjs](https://github.com/vinhWater/tts-fe-nextjs)

## Project Overview

This project provides a comprehensive admin dashboard for managing TikTok Shop products, categories, brands, and more. It consists of a NestJS backend and a Next.js frontend.

## Features

- Admin dashboard with product management
- Table components with multi-filter, multi-selection, and bulk edit capabilities
- Category and brand management
- TikTok Shop integration
- Authentication system
- Responsive design

## Tech Stack

- **Framework**: Next.js 14 (React)
- **State Management**: Zustand
- **UI Library**: Shadcn/ui with Tailwind CSS
- **API Client**: Axios
- **Table**: TanStack Table (React Table)

## Getting Started

### Prerequisites

- Node.js 18.17.0 or later
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
# or
yarn install
```

3. Create a `.env.local` file in the root directory with the following variables:

```
NEXT_PUBLIC_API_URL=http://localhost:3000
```

4. Start the development server:

```bash
npm run dev
# or
yarn dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

- `src/app`: Next.js App Router pages
- `src/components`: React components
  - `src/components/ui`: Shadcn UI components
  - `src/components/layout`: Layout components
  - `src/components/products`: Product-related components
- `src/lib`: Utility functions and hooks
  - `src/lib/api`: API client and services
  - `src/lib/hooks`: Custom React hooks
  - `src/lib/store`: Zustand state management

## Development

### Adding New Pages

1. Create a new file in the `src/app` directory
2. Export a React component as the default export

### Adding New Components

1. Create a new file in the `src/components` directory
2. Export the component

### Adding New API Services

1. Create a new file in the `src/lib/api` directory
2. Export the API functions

## Building for Production

```bash
npm run build
# or
yarn build
```

## Deployment

The application can be deployed to any platform that supports Next.js applications, such as Vercel, Netlify, or a custom server.

```bash
npm run start
# or
yarn start
```
