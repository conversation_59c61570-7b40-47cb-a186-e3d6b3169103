# Dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
.next/
out/

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
logs/
*.log

# Testing
coverage/

# Temporary files
tmp/
temp/

# Database
*.sqlite

# Nested Git repositories (handle separately)
tts-be-nestjs/
tts-fe-nextjs/
