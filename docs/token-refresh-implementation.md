# Token Refresh Implementation

This document outlines the token refresh implementation in the TikTok Shop application.

## Overview

The token refresh implementation provides a secure way to maintain user sessions without requiring frequent re-authentication. It uses a combination of short-lived access tokens and longer-lived refresh tokens.

## Key Components

1. **Access Tokens**
   - Short-lived JWT tokens (15 minutes)
   - Used for API authentication
   - Contain user ID and email in payload

2. **Refresh Tokens**
   - Longer-lived tokens (7 days)
   - Stored in database with user association
   - Used to obtain new access tokens when they expire

3. **Token Storage**
   - Access tokens are stored client-side (browser)
   - Refresh tokens are stored both client-side and in the database

## Authentication Flow

1. **Login**
   - User logs in via OAuth or magic link
   - Backend generates and returns both access and refresh tokens
   - Frontend stores both tokens

2. **API Requests**
   - Frontend includes access token in Authorization header
   - Backend validates token for each request

3. **Token Refresh**
   - When access token expires, frontend uses refresh token to get a new pair
   - Backend validates refresh token, revokes it, and issues new tokens
   - Frontend updates stored tokens

4. **Logout**
   - Frontend calls logout endpoint with refresh token
   - Backend revokes the refresh token
   - Frontend clears stored tokens

## API Endpoints

### `/auth/refresh-token` (POST)
- **Purpose**: Get new access and refresh tokens
- **Request Body**: `{ "refreshToken": "string" }`
- **Response**: `{ "accessToken": "string", "refreshToken": "string", "expiresIn": number, "tokenType": "Bearer", "user": { "id": number, "email": "string", "name": "string" } }`

### `/auth/logout` (POST)
- **Purpose**: Revoke a refresh token
- **Request Body**: `{ "refreshToken": "string" }`
- **Response**: `{ "success": true, "message": "Logged out successfully" }`

### `/auth/logout-all` (POST)
- **Purpose**: Revoke all refresh tokens for the current user
- **Request Body**: Empty
- **Response**: `{ "success": true, "message": "Logged out from all devices successfully" }`

## Security Considerations

1. **Token Rotation**
   - Refresh tokens are single-use (revoked after use)
   - New refresh token is issued with each refresh

2. **Token Expiration**
   - Access tokens expire after 15 minutes
   - Refresh tokens expire after 7 days

3. **Token Revocation**
   - Tokens can be revoked individually or all at once
   - Revoked tokens are marked in the database

## Frontend Integration

The frontend should:
1. Store both tokens securely
2. Monitor access token expiration
3. Automatically refresh tokens when needed
4. Handle failed refresh attempts (e.g., redirect to login)

## Database Schema

The `refresh_tokens` table stores:
- `id`: Primary key
- `token`: Unique token string
- `userId`: Foreign key to users table
- `isRevoked`: Boolean flag for revocation status
- `expiresAt`: Timestamp for token expiration
- `createdAt`: Timestamp for token creation

## Implementation Notes

- Token refresh should happen automatically in the background
- Failed refreshes should redirect to login
- Consider implementing a token refresh queue to prevent race conditions
